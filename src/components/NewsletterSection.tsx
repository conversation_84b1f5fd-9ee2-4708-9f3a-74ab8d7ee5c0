
import { But<PERSON> } from '@/components/ui/button';
import { Award, Users, Globe, Shield } from 'lucide-react';
import { Link } from 'react-router-dom';

const NewsletterSection = () => {
  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left side - Large photo */}
          <div className="relative">
            <img
              src="https://images.unsplash.com/photo-1649972904349-6e44c42644a7?w=800&h=600&fit=crop"
              alt="Medical professional using technology"
              className="w-full h-96 object-cover rounded-lg shadow-lg"
            />
            <div className="absolute inset-0 bg-gradient-to-r from-black/20 to-transparent rounded-lg"></div>
          </div>

          {/* Right side - Content and features */}
          <div className="space-y-6">
            <div className="text-center lg:text-left">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">Why Choose Medtronic</h2>
              <p className="text-lg text-gray-700 mb-8">
                Leading the way in medical technology innovation with trusted solutions for healthcare providers worldwide
              </p>
            </div>

            {/* Features Grid */}
            <div className="grid grid-cols-2 gap-6">
              <div className="text-center lg:text-left">
                <div className="bg-blue-100 w-12 h-12 rounded-lg flex items-center justify-center mb-3 mx-auto lg:mx-0">
                  <Award className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Industry Leader</h3>
                <p className="text-sm text-gray-600">Over 19 years of excellence in medical technology</p>
              </div>

              <div className="text-center lg:text-left">
                <div className="bg-green-100 w-12 h-12 rounded-lg flex items-center justify-center mb-3 mx-auto lg:mx-0">
                  <Users className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Expert Support</h3>
                <p className="text-sm text-gray-600">24/7 technical support and training programs</p>
              </div>

              <div className="text-center lg:text-left">
                <div className="bg-purple-100 w-12 h-12 rounded-lg flex items-center justify-center mb-3 mx-auto lg:mx-0">
                  <Globe className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Global Reach</h3>
                <p className="text-sm text-gray-600">Serving healthcare facilities across Nepal and beyond</p>
              </div>

              <div className="text-center lg:text-left">
                <div className="bg-orange-100 w-12 h-12 rounded-lg flex items-center justify-center mb-3 mx-auto lg:mx-0">
                  <Shield className="h-6 w-6 text-orange-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Quality Assured</h3>
                <p className="text-sm text-gray-600">ISO certified and regulatory compliant solutions</p>
              </div>
            </div>

            <div className="pt-4">
              <Link to="/about-us">
                <Button className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg text-lg">
                  Learn More About Us
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default NewsletterSection;
