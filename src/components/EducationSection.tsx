
import { Button } from '@/components/ui/button';
import { Plus, ArrowUp } from 'lucide-react';

const EducationSection = () => {
  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-4xl font-bold text-center text-gray-900 mb-12">Education for ultrasound</h2>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
          {/* Left side - theSUITE card */}
          <div className="bg-slate-900 rounded-lg p-8 text-white relative overflow-hidden">
            <div className="relative z-10">
              {/* GEFOG Health Foundation logo */}
              <div className="flex items-center mb-6">
                <div className="w-8 h-8 bg-purple-600 rounded-full mr-3 flex items-center justify-center">
                  <div className="w-4 h-4 bg-white rounded-full"></div>
                </div>
                <span className="text-sm">GEFOG Health Foundation</span>
              </div>
              
              <h3 className="text-3xl font-bold mb-8">ISUOG Twin Guideline update</h3>
              
              {/* Speakers grid */}
              <div className="grid grid-cols-2 gap-6 mb-8">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-gray-300 rounded-full overflow-hidden">
                    <img 
                      src="https://images.unsplash.com/photo-**********-2b71ea197ec2?w=50&h=50&fit=crop&crop=face" 
                      alt="Prof. Asma Khalil" 
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div>
                    <p className="font-semibold text-sm">Prof. Asma Khalil</p>
                    <p className="text-xs text-gray-300">St. George's Univ. Hospital, UK</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-gray-300 rounded-full overflow-hidden">
                    <img 
                      src="https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=50&h=50&fit=crop&crop=face" 
                      alt="Prof. Conrado Coutinho" 
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div>
                    <p className="font-semibold text-sm">Prof. Conrado Coutinho</p>
                    <p className="text-xs text-gray-300">University of São Paulo, Brazil</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-gray-300 rounded-full overflow-hidden">
                    <img 
                      src="https://images.unsplash.com/photo-1582750433449-648ed127bb54?w=50&h=50&fit=crop&crop=face" 
                      alt="Prof. Tak Yeung LEUNG" 
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div>
                    <p className="font-semibold text-sm">Prof. Tak Yeung LEUNG</p>
                    <p className="text-xs text-gray-300">The Chinese University of Hong Kong, Hong Kong SAR China</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-gray-300 rounded-full overflow-hidden">
                    <img 
                      src="https://images.unsplash.com/photo-1594824092813-b2669011b66e?w=50&h=50&fit=crop&crop=face" 
                      alt="Ms. Stephanie Ernst" 
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div>
                    <p className="font-semibold text-sm">Ms. Stephanie Ernst</p>
                    <p className="text-xs text-gray-300">Founder of the TAPS Support Foundation, UK</p>
                  </div>
                </div>
              </div>
              
              {/* theSUITE branding */}
              <div className="text-2xl font-bold">
                the<span className="bg-white text-slate-900 px-1 rounded">SUITE</span>
              </div>
            </div>
            
            {/* Background pattern */}
            <div className="absolute inset-0 opacity-10">
              <svg className="w-full h-full" viewBox="0 0 400 300">
                <defs>
                  <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
                    <path d="M 40 0 L 0 0 0 40" fill="none" stroke="currentColor" strokeWidth="1"/>
                  </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#grid)" />
                <circle cx="350" cy="50" r="80" fill="none" stroke="currentColor" strokeWidth="2"/>
                <circle cx="300" cy="150" r="60" fill="none" stroke="currentColor" strokeWidth="2"/>
                <circle cx="380" cy="200" r="40" fill="none" stroke="currentColor" strokeWidth="2"/>
              </svg>
            </div>
          </div>
          
          {/* Right side - Education list */}
          <div className="space-y-6">
            <div className="flex items-start space-x-4 p-4 hover:bg-white hover:shadow-sm rounded-lg transition-all cursor-pointer">
              <span className="text-2xl font-bold text-gray-300">01</span>
              <div className="flex-1">
                <h4 className="text-xl font-semibold text-gray-900 mb-2">ISUOG Twin Guideline update</h4>
                <button className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center">
                  Learn more →
                </button>
              </div>
            </div>
            
            <hr className="border-gray-200" />
            
            <div className="flex items-start space-x-4 p-4 hover:bg-white hover:shadow-sm rounded-lg transition-all cursor-pointer">
              <span className="text-2xl font-bold text-gray-300">02</span>
              <div className="flex-1">
                <h4 className="text-xl font-semibold text-gray-900">Prof. Baski Thilaganathan on HERA Z20</h4>
              </div>
            </div>
            
            <div className="flex items-start space-x-4 p-4 hover:bg-white hover:shadow-sm rounded-lg transition-all cursor-pointer">
              <span className="text-2xl font-bold text-gray-300">03</span>
              <div className="flex-1">
                <h4 className="text-xl font-semibold text-gray-900">Fetal face with PortraitVue™</h4>
              </div>
            </div>
            
            <div className="flex items-start space-x-4 p-4 hover:bg-white hover:shadow-sm rounded-lg transition-all cursor-pointer">
              <span className="text-2xl font-bold text-gray-300">04</span>
              <div className="flex-1">
                <h4 className="text-xl font-semibold text-gray-900">Tips & Tricks for EzVolume™</h4>
              </div>
            </div>
          </div>
        </div>
        
        {/* Floating action buttons */}
        <div className="fixed bottom-8 right-8 flex flex-col space-y-4">
          <Button size="icon" className="w-12 h-12 rounded-full bg-black hover:bg-gray-800 text-white">
            <Plus className="h-5 w-5" />
          </Button>
          <Button size="icon" variant="outline" className="w-12 h-12 rounded-full bg-gray-500 hover:bg-gray-600 text-white border-none">
            <ArrowUp className="h-5 w-5" />
          </Button>
        </div>
      </div>
    </section>
  );
};

export default EducationSection;
