
import { useState } from 'react';
import { Link } from 'react-router-dom';
import Navigation from './header/Navigation';
import ProductsMenu from './header/ProductsMenu';
import CategoriesMenu from './header/CategoriesMenu';
import ResourcesMenu from './header/ResourcesMenu';
import SupportMenu from './header/SupportMenu';
import AboutUsMenu from './header/AboutUsMenu';

const Header = () => {
  const [activeMenu, setActiveMenu] = useState<string | null>(null);

  const handleMenuEnter = (menu: string) => {
    setActiveMenu(menu);
  };

  const handleMenuLeave = () => {
    setActiveMenu(null);
  };

  return (
    <header className="bg-white shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center">
            <Link to="/" className="text-2xl font-bold text-gray-900">MEDTRONIC</Link>
          </div>
          
          <Navigation onMenuEnter={handleMenuEnter} />
        </div>
      </div>

      {/* Menu Components */}
      {activeMenu === 'products' && <ProductsMenu onClose={handleMenuLeave} />}
      {activeMenu === 'categories' && <CategoriesMenu onClose={handleMenuLeave} />}
      {activeMenu === 'resources' && <ResourcesMenu onClose={handleMenuLeave} />}
      {activeMenu === 'support' && <SupportMenu onClose={handleMenuLeave} />}
      {activeMenu === 'aboutus' && <AboutUsMenu onClose={handleMenuLeave} />}
    </header>
  );
};

export default Header;
