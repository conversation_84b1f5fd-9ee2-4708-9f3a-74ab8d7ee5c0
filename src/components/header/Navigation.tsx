
import { Link } from 'react-router-dom';

interface NavigationProps {
  onMenuEnter: (menu: string) => void;
}

const Navigation = ({ onMenuEnter }: NavigationProps) => {
  return (
    <nav className="hidden md:flex space-x-8">
      <button 
        className="text-gray-900 hover:text-gray-700 font-medium"
        onMouseEnter={() => onMenuEnter('products')}
      >
        Products & Solutions
      </button>
      <button 
        className="text-gray-700 hover:text-gray-900"
        onMouseEnter={() => onMenuEnter('categories')}
      >
        Medical Categories
      </button>
      <button 
        className="text-gray-700 hover:text-gray-900"
        onMouseEnter={() => onMenuEnter('resources')}
      >
        Resources
      </button>
      <button 
        className="text-gray-700 hover:text-gray-900"
        onMouseEnter={() => onMenuEnter('support')}
      >
        Support
      </button>
      <button 
        className="text-gray-700 hover:text-gray-900"
        onMouseEnter={() => onMenuEnter('aboutus')}
      >
        About Us
      </button>
      <Link 
        to="/contact" 
        className="text-gray-700 hover:text-gray-900"
      >
        Contact Us
      </Link>
    </nav>
  );
};

export default Navigation;
