
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';
import { Link } from 'react-router-dom';

interface AboutUsMenuProps {
  onClose: () => void;
}

const AboutUsMenu = ({ onClose }: AboutUsMenuProps) => {
  return (
    <div 
      className="absolute top-16 left-0 w-full bg-white shadow-lg z-50 border-t"
      onMouseLeave={onClose}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-between items-start">
          <div className="flex-1 max-w-md">
            <h1 className="text-3xl font-bold text-gray-900 mb-8">About Us</h1>

            <div className="space-y-8">
              <div className="space-y-4">
                <Link to="/medtronic-healthcare" className="block text-gray-600 hover:text-gray-900 transition-colors">
                  Medtronic Healthcare
                </Link>
                <Link to="/news-center" className="block text-gray-600 hover:text-gray-900 transition-colors">
                  News Center
                </Link>
                <Link to="/events" className="block text-gray-600 hover:text-gray-900 transition-colors">
                  Events
                </Link>
                <Link to="/sustainability" className="block text-gray-600 hover:text-gray-900 transition-colors">
                  Sustainability ↗
                </Link>
              </div>
            </div>
          </div>

          <div className="ml-8 w-80">
            <div className="bg-white rounded-lg overflow-hidden shadow-sm">
              <img 
                src="https://images.unsplash.com/photo-1519389950473-47ba0277781c?w=400&h=250&fit=crop" 
                alt="Medical professionals collaborating" 
                className="w-full h-64 object-cover"
              />
              <div className="p-6">
                <p className="text-gray-900 font-medium leading-relaxed">
                  Learn about our company and our latest news in the medical technology industry...
                </p>
              </div>
            </div>
          </div>

          <Button 
            variant="ghost" 
            size="sm" 
            className="absolute top-4 right-4"
            onClick={onClose}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AboutUsMenu;
