
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';

const subcategories = {
  cardiovascular: {
    title: "Cardiovascular",
    description: "Advanced cardiac imaging and echocardiography solutions",
    products: [
      {
        name: "Cardiac Ultrasound Pro",
        image: "https://images.unsplash.com/photo-1605810230434-7631ac76ec81?w=400&h=300&fit=crop",
        description: "High-end cardiac imaging system with advanced Doppler capabilities for comprehensive cardiovascular diagnostics.",
        detailedInfo: "The Cardiac Ultrasound Pro features state-of-the-art transducers, real-time 3D imaging, advanced strain analysis, and comprehensive cardiac quantification tools. Perfect for cardiology departments and specialized cardiac centers."
      },
      {
        name: "Echo Portable",
        image: "https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?w=400&h=300&fit=crop",
        description: "Portable echocardiography system perfect for bedside cardiac assessments and emergency situations.",
        detailedInfo: "Lightweight and compact design with full cardiac imaging capabilities. Features wireless connectivity, long battery life, and specialized cardiac presets for emergency and ICU environments."
      }
    ]
  },
  generalImaging: {
    title: "General Imaging",
    description: "Comprehensive ultrasound solutions for general diagnostic imaging",
    products: [
      {
        name: "MultiVision Elite",
        image: "/lovable-uploads/e4a1f2a1-33dc-4b92-980b-5e6e6f0aceef.png",
        description: "Versatile ultrasound system for general radiology departments with multi-frequency transducers.",
        detailedInfo: "Advanced platform supporting multiple imaging modes including B-mode, Color Doppler, Power Doppler, and elastography. Suitable for abdominal, superficial organs, and vascular imaging with exceptional image quality."
      },
      {
        name: "SonoScan Pro",
        image: "https://images.unsplash.com/photo-1518770660439-4636190af475?w=400&h=300&fit=crop",
        description: "Advanced general imaging platform with superior image quality and workflow optimization.",
        detailedInfo: "Premium imaging system with AI-enhanced image optimization, automated measurements, and comprehensive reporting tools. Ideal for high-volume imaging centers and hospitals."
      }
    ]
  },
  pediatrics: {
    title: "Pediatrics",
    description: "Specialized imaging protocols for pediatric patients",
    products: [
      {
        name: "PediScan Junior",
        image: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=400&h=300&fit=crop",
        description: "Child-friendly ultrasound system with specialized pediatric presets and smaller transducers.",
        detailedInfo: "Designed specifically for pediatric imaging with age-appropriate presets, smaller transducers, and child-friendly interface. Features specialized modes for neonatal, infant, and adolescent imaging."
      },
      {
        name: "KidCare Portable",
        image: "https://images.unsplash.com/photo-1531297484001-80022131f5a1?w=400&h=300&fit=crop",
        description: "Portable ultrasound solution designed specifically for pediatric emergency and NICU applications.",
        detailedInfo: "Ultra-portable system optimized for pediatric emergency medicine and NICU environments. Features specialized neonatal presets, high-frequency transducers, and infection control design."
      }
    ]
  },
  pointOfCare: {
    title: "Point of Care",
    description: "Portable solutions for bedside and emergency diagnostics",
    products: [
      {
        name: "QuickScan Mobile",
        image: "https://images.unsplash.com/photo-1531297484001-80022131f5a1?w=400&h=300&fit=crop",
        description: "Ultra-portable handheld ultrasound device for immediate point-of-care diagnostics.",
        detailedInfo: "Handheld device with smartphone connectivity, cloud-based image storage, and AI-assisted diagnostics. Perfect for emergency medicine, critical care, and rural healthcare settings."
      },
      {
        name: "EmergencyView Pro",
        image: "https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?w=400&h=300&fit=crop",
        description: "Rugged portable ultrasound system designed for emergency departments and ambulance use.",
        detailedInfo: "Military-grade durability with emergency-focused presets including FAST, cardiac, lung, and vascular protocols. Features rapid boot-up, long battery life, and EMR integration."
      }
    ]
  },
  womensHealth: {
    title: "Women's Health",
    description: "Specialized obstetric and gynecological imaging capabilities",
    products: [
      {
        name: "OB/GYN Excellence",
        image: "https://images.unsplash.com/photo-1649972904349-6e44c42644a7?w=400&h=300&fit=crop",
        description: "Comprehensive women's health imaging system with advanced 3D/4D capabilities for obstetrics.",
        detailedInfo: "Premier women's health platform featuring 3D/4D imaging, fetal biometry calculations, advanced Doppler studies, and comprehensive OB/GYN measurement packages with automated reporting."
      },
      {
        name: "Maternal Care Plus",
        image: "https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?w=400&h=300&fit=crop",
        description: "Specialized ultrasound platform for maternal-fetal medicine with enhanced diagnostic features.",
        detailedInfo: "Advanced maternal-fetal medicine system with specialized protocols for high-risk pregnancies, detailed fetal assessment tools, and comprehensive maternal monitoring capabilities."
      }
    ]
  }
};

const UltrasoundProductsSection = () => {
  const handleDownloadBrochure = (productName: string) => {
    // Create a temporary link element to trigger download
    const link = document.createElement('a');
    link.href = '/placeholder.svg'; // Using placeholder as demo file
    link.download = `${productName.replace(/\s+/g, '-')}-Brochure.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Show success message
    alert(`Downloading ${productName} brochure...`);
  };

  return (
    <div className="mb-20">
      <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">Products & Solutions by Category</h2>
      
      <Tabs defaultValue="cardiovascular" className="w-full">
        <TabsList className="grid w-full grid-cols-5 mb-8">
          <TabsTrigger value="cardiovascular">Cardiovascular</TabsTrigger>
          <TabsTrigger value="generalImaging">General Imaging</TabsTrigger>
          <TabsTrigger value="pediatrics">Pediatrics</TabsTrigger>
          <TabsTrigger value="pointOfCare">Point of Care</TabsTrigger>
          <TabsTrigger value="womensHealth">Women's Health</TabsTrigger>
        </TabsList>
        
        {Object.entries(subcategories).map(([key, category]) => (
          <TabsContent key={key} value={key} className="mt-8">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">{category.title}</h3>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">{category.description}</p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {category.products.map((product, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow">
                  <CardHeader className="p-0">
                    <img 
                      src={product.image} 
                      alt={product.name}
                      className="w-full h-64 object-cover rounded-t-lg"
                    />
                  </CardHeader>
                  <CardContent className="p-6">
                    <CardTitle className="text-xl mb-3">{product.name}</CardTitle>
                    <CardDescription className="text-gray-600 leading-relaxed mb-4">
                      {product.description}
                    </CardDescription>
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button variant="outline" className="w-full">
                          Learn More
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="sm:max-w-[600px]">
                        <DialogHeader>
                          <DialogTitle className="text-2xl">{product.name}</DialogTitle>
                          <DialogDescription asChild>
                            <div className="space-y-4">
                              <img 
                                src={product.image} 
                                alt={product.name}
                                className="w-full h-48 object-cover rounded-lg"
                              />
                              <p className="text-base text-gray-700 leading-relaxed">
                                {product.detailedInfo}
                              </p>
                              <div className="flex gap-2 mt-6">
                                <Button className="flex-1">Contact Sales</Button>
                                <Button
                                  variant="outline"
                                  className="flex-1"
                                  onClick={() => handleDownloadBrochure(product.name)}
                                >
                                  Download Brochure
                                </Button>
                              </div>
                            </div>
                          </DialogDescription>
                        </DialogHeader>
                      </DialogContent>
                    </Dialog>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
};

export default UltrasoundProductsSection;
