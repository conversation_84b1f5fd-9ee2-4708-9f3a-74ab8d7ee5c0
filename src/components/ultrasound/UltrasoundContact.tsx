
import { But<PERSON> } from '@/components/ui/button';
import { Phone, Mail, MessageCircle } from 'lucide-react';
import { Link } from 'react-router-dom';

const UltrasoundContact = () => {
  return (
    <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-2xl p-8 lg:p-12">
      <div className="text-center mb-10">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">Ready to Purchase?</h2>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Get in touch with our sales team to learn more about our Ultrasound systems and find the perfect solution for your medical facility.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="text-center p-6 bg-white rounded-lg shadow-sm">
          <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
            <Phone className="h-8 w-8 text-purple-600" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Call Sales</h3>
          <p className="text-gray-600 mb-4">Speak directly with our product specialists</p>
          <Button variant="outline" className="border-purple-600 text-purple-600 hover:bg-purple-600 hover:text-white">
            +****************
          </Button>
        </div>

        <div className="text-center p-6 bg-white rounded-lg shadow-sm">
          <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
            <Mail className="h-8 w-8 text-purple-600" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Email Inquiry</h3>
          <p className="text-gray-600 mb-4">Get detailed product information</p>
          <Button variant="outline" className="border-purple-600 text-purple-600 hover:bg-purple-600 hover:text-white">
            <EMAIL>
          </Button>
        </div>

        <div className="text-center p-6 bg-white rounded-lg shadow-sm">
          <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
            <MessageCircle className="h-8 w-8 text-purple-600" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Request Quote</h3>
          <p className="text-gray-600 mb-4">Get a customized quote for your needs</p>
          <Link to="/contact">
            <Button className="bg-purple-600 hover:bg-purple-700 text-white">
              Contact Us
            </Button>
          </Link>
        </div>
      </div>

      <div className="text-center">
        <Link to="/contact">
          <Button size="lg" className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-3">
            Get Started Today
          </Button>
        </Link>
      </div>
    </div>
  );
};

export default UltrasoundContact;
