
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';

const subcategories = {
  generalRadiography: {
    title: "General Radiography",
    description: "Advanced digital radiography solutions for comprehensive diagnostic imaging",
    products: [
      {
        name: "AccE GC85A",
        image: "https://images.unsplash.com/photo-1518770660439-4636190af475?w=400&h=300&fit=crop",
        description: "Premium ceiling-mounted digital radiography system with advanced image processing capabilities.",
        detailedInfo: "The AccE GC85A features state-of-the-art ceiling suspension design with motorized positioning, high-resolution flat panel detector, and advanced image processing algorithms. Perfect for high-volume general radiography departments requiring maximum efficiency and image quality."
      },
      {
        name: "AccE GM85",
        image: "https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=400&h=300&fit=crop",
        description: "Versatile floor-mounted digital radiography system designed for comprehensive imaging applications.",
        detailedInfo: "Advanced floor-mounted system with flexible positioning capabilities, high-quality cesium iodide detector, and intuitive user interface. Ideal for hospitals and imaging centers requiring versatile radiographic capabilities with exceptional image quality."
      }
    ]
  },
  portableImaging: {
    title: "Portable Imaging",
    description: "Mobile digital radiography solutions for bedside and emergency imaging",
    products: [
      {
        name: "GM85 Fit",
        image: "https://images.unsplash.com/photo-1531297484001-80022131f5a1?w=400&h=300&fit=crop",
        description: "Compact and lightweight mobile digital radiography system for bedside imaging.",
        detailedInfo: "Ultra-portable design with wireless connectivity, long-lasting battery, and ergonomic handling. Features advanced image processing, automated exposure control, and seamless integration with hospital information systems. Perfect for ICU, emergency departments, and mobile imaging services."
      },
      {
        name: "GR40CW",
        image: "https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?w=400&h=300&fit=crop",
        description: "Advanced portable chest imaging system with wireless capabilities for critical care environments.",
        detailedInfo: "Specialized portable system optimized for chest imaging with wireless detector technology, rapid image acquisition, and enhanced portability. Features infection control design, intuitive touch interface, and seamless workflow integration for critical care and emergency imaging."
      }
    ]
  },
  detectorSolutions: {
    title: "Detector Solutions",
    description: "High-performance digital detectors for existing radiography systems",
    products: [
      {
        name: "Digital Detector Pro",
        image: "https://images.unsplash.com/photo-1487058792275-0ad4aaf24ca7?w=400&h=300&fit=crop",
        description: "Premium flat panel detector for retrofit applications and system upgrades.",
        detailedInfo: "High-resolution cesium iodide detector with exceptional image quality, fast readout time, and durable construction. Compatible with most existing radiography systems, providing an upgrade path to digital imaging with minimal infrastructure changes."
      },
      {
        name: "Wireless Detector Elite",
        image: "https://images.unsplash.com/photo-1605810230434-7631ac76ec81?w=400&h=300&fit=crop",
        description: "Wireless digital detector for maximum flexibility and workflow efficiency.",
        detailedInfo: "Cutting-edge wireless detector technology with rapid image transmission, long battery life, and rugged design. Features automatic exposure recognition, advanced image processing, and seamless integration with digital radiography systems."
      }
    ]
  }
};

const DigitalRadiographyProductsSection = () => {
  const handleDownloadBrochure = (productName: string) => {
    // Create a temporary link element to trigger download
    const link = document.createElement('a');
    link.href = '/placeholder.svg'; // Using placeholder as demo file
    link.download = `${productName.replace(/\s+/g, '-')}-Brochure.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Show success message
    alert(`Downloading ${productName} brochure...`);
  };

  return (
    <div className="mb-20">
      <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">Digital Radiography Products & Solutions</h2>
      
      <Tabs defaultValue="generalRadiography" className="w-full">
        <TabsList className="grid w-full grid-cols-3 mb-8">
          <TabsTrigger value="generalRadiography">General Radiography</TabsTrigger>
          <TabsTrigger value="portableImaging">Portable Imaging</TabsTrigger>
          <TabsTrigger value="detectorSolutions">Detector Solutions</TabsTrigger>
        </TabsList>
        
        {Object.entries(subcategories).map(([key, category]) => (
          <TabsContent key={key} value={key} className="mt-8">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">{category.title}</h3>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">{category.description}</p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {category.products.map((product, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow">
                  <CardHeader className="p-0">
                    <img 
                      src={product.image} 
                      alt={product.name}
                      className="w-full h-64 object-cover rounded-t-lg"
                    />
                  </CardHeader>
                  <CardContent className="p-6">
                    <CardTitle className="text-xl mb-3">{product.name}</CardTitle>
                    <CardDescription className="text-gray-600 leading-relaxed mb-4">
                      {product.description}
                    </CardDescription>
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button variant="outline" className="w-full">
                          Learn More
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="sm:max-w-[600px]">
                        <DialogHeader>
                          <DialogTitle className="text-2xl">{product.name}</DialogTitle>
                          <DialogDescription asChild>
                            <div className="space-y-4">
                              <img 
                                src={product.image} 
                                alt={product.name}
                                className="w-full h-48 object-cover rounded-lg"
                              />
                              <p className="text-base text-gray-700 leading-relaxed">
                                {product.detailedInfo}
                              </p>
                              <div className="flex gap-2 mt-6">
                                <Button className="flex-1">Contact Sales</Button>
                                <Button
                                  variant="outline"
                                  className="flex-1"
                                  onClick={() => handleDownloadBrochure(product.name)}
                                >
                                  Download Brochure
                                </Button>
                              </div>
                            </div>
                          </DialogDescription>
                        </DialogHeader>
                      </DialogContent>
                    </Dialog>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
};

export default DigitalRadiographyProductsSection;
