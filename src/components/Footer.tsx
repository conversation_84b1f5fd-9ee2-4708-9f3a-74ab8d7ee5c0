
const Footer = () => {
  return (
    <footer className="bg-white border-t">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <h3 className="font-semibold text-gray-900 mb-4">Products</h3>
            <ul className="space-y-2">
              <li><a href="#" className="text-gray-600 hover:text-gray-900">Ultrasound System</a></li>
              <li><a href="#" className="text-gray-600 hover:text-gray-900">Digital Radiography</a></li>
              <li><a href="#" className="text-gray-600 hover:text-gray-900">Computed Tomography</a></li>
            </ul>
          </div>

          <div>
            <h3 className="font-semibold text-gray-900 mb-4">Solutions</h3>
            <ul className="space-y-2">
              <li><a href="#" className="text-gray-600 hover:text-gray-900">S-Hub</a></li>
              <li><a href="#" className="text-gray-600 hover:text-gray-900">Smart Center</a></li>
              <li><a href="#" className="text-gray-600 hover:text-gray-900">Cyber Security</a></li>
              <li><a href="#" className="text-gray-600 hover:text-gray-900">Liver Analysis Solutions</a></li>
              <li><a href="#" className="text-gray-600 hover:text-gray-900">Value-up Package</a></li>
            </ul>
          </div>

          <div>
            <h3 className="font-semibold text-gray-900 mb-4">Resources</h3>
            <ul className="space-y-2">
              <li><a href="#" className="text-gray-600 hover:text-gray-900">theSUITE</a></li>
              <li><a href="#" className="text-gray-600 hover:text-gray-900">Video</a></li>
              <li><a href="#" className="text-gray-600 hover:text-gray-900">Insight</a></li>
              <li><a href="#" className="text-gray-600 hover:text-gray-900">Cleaning Guidelines</a></li>
              <li><a href="#" className="text-gray-600 hover:text-gray-900">Catalogue & Software</a></li>
              <li><a href="#" className="text-gray-600 hover:text-gray-900">Manual</a></li>
            </ul>
          </div>

          <div>
            <h3 className="font-semibold text-gray-900 mb-4">About Us</h3>
            <ul className="space-y-2">
              <li><a href="#" className="text-gray-600 hover:text-gray-900">Medtronic Healthcare</a></li>
              <li><a href="#" className="text-gray-600 hover:text-gray-900">News Center</a></li>
              <li><a href="#" className="text-gray-600 hover:text-gray-900">Events</a></li>
              <li><a href="#" className="text-gray-600 hover:text-gray-900">Sustainability</a></li>
            </ul>
          </div>
        </div>

        <div className="border-t mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-600 text-sm">Copyright © 2024-2025 Medtronic. All rights reserved.</p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a href="#" className="text-gray-600 hover:text-gray-900 text-sm">Privacy policy</a>
              <a href="#" className="text-gray-600 hover:text-gray-900 text-sm">Terms of use</a>
              <a href="#" className="text-gray-600 hover:text-gray-900 text-sm">Business ethics</a>
              <a href="#" className="text-gray-600 hover:text-gray-900 text-sm">Business guidelines</a>
              <a href="#" className="text-gray-600 hover:text-gray-900 text-sm">Cookie policy</a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
