
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';

const HeroCarousel = () => {
  const [currentSlide, setCurrentSlide] = useState(0);

  const slides = [
    {
      id: 1,
      title: "EleV8 to Excellence",
      subtitle: "Receive exclusive saving offers simply by submitting a demo request for Samsung's latest ultrasound innovation, V8.",
      buttonText: "Request Demo",
      image: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=600&h=400&fit=crop",
      bgGradient: "from-purple-600 to-blue-600"
    },
    {
      id: 2,
      title: "Ultrasound System",
      subtitle: "Advanced imaging technology for precise diagnostics and enhanced patient care.",
      buttonText: "Learn More",
      image: "https://images.unsplash.com/photo-**********-5c350d0d3c56?w=600&h=400&fit=crop",
      bgGradient: "from-blue-600 to-cyan-600"
    },
    {
      id: 3,
      title: "Digital Radiography",
      subtitle: "State-of-the-art digital imaging solutions for comprehensive medical diagnostics.",
      buttonText: "Explore Features",
      image: "https://images.unsplash.com/photo-1518770660439-4636190af475?w=600&h=400&fit=crop",
      bgGradient: "from-gray-700 to-gray-900"
    },
    {
      id: 4,
      title: "Patient Monitors",
      subtitle: "Real-time monitoring solutions designed for enhanced patient monitoring excellence.",
      buttonText: "View Products",
      image: "https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=600&h=400&fit=crop",
      bgGradient: "from-green-600 to-teal-600"
    }
  ];

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
  };

  useEffect(() => {
    const timer = setInterval(nextSlide, 5000);
    return () => clearInterval(timer);
  }, []);

  return (
    <section className="relative h-96 md:h-[500px] overflow-hidden bg-gray-50">
      <div className="relative w-full h-full">
        {slides.map((slide, index) => (
          <div
            key={slide.id}
            className={`absolute inset-0 transition-transform duration-500 ease-in-out ${
              index === currentSlide ? 'translate-x-0' : 
              index < currentSlide ? '-translate-x-full' : 'translate-x-full'
            }`}
          >
            <div className={`w-full h-full bg-gradient-to-r ${slide.bgGradient} relative overflow-hidden`}>
              {/* Background Image */}
              <div className="absolute inset-0 opacity-20">
                <img 
                  src={slide.image} 
                  alt={slide.title}
                  className="w-full h-full object-cover"
                />
              </div>
              
              {/* Content */}
              <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center h-full">
                  {/* Left side - Text content */}
                  <div className="text-white space-y-6">
                    <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
                      {slide.title}
                    </h1>
                    <p className="text-lg md:text-xl text-gray-200 max-w-lg">
                      {slide.subtitle}
                    </p>
                    <Button 
                      size="lg" 
                      className="bg-white text-gray-900 hover:bg-gray-100 px-8 py-3 text-lg rounded-full"
                    >
                      {slide.buttonText}
                    </Button>
                  </div>

                  {/* Right side - Product image */}
                  <div className="hidden lg:flex justify-center items-center">
                    <div className="relative">
                      <img 
                        src={slide.image} 
                        alt={slide.title}
                        className="w-80 h-64 object-cover rounded-lg shadow-2xl"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-lg"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Navigation arrows */}
      <button
        onClick={prevSlide}
        className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white p-2 rounded-full transition-colors"
      >
        <ChevronLeft className="w-6 h-6" />
      </button>
      <button
        onClick={nextSlide}
        className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white p-2 rounded-full transition-colors"
      >
        <ChevronRight className="w-6 h-6" />
      </button>

      {/* Slide indicators */}
      <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-2">
        {slides.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentSlide(index)}
            className={`w-3 h-3 rounded-full transition-colors ${
              index === currentSlide ? 'bg-white' : 'bg-white/50'
            }`}
          />
        ))}
      </div>
    </section>
  );
};

export default HeroCarousel;
