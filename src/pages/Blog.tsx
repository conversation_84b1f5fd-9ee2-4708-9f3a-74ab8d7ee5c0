import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Calendar, ArrowRight, User, Search, Filter } from 'lucide-react';
import { useState } from 'react';

const Blog = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');

  const blogPosts = [
    {
      id: 1,
      title: "Revolutionary AI-Powered Ultrasound Technology Transforms Cardiac Imaging",
      excerpt: "Discover how our latest ultrasound system with integrated AI algorithms is revolutionizing cardiac diagnostics, providing real-time analysis and improving patient outcomes across healthcare facilities worldwide.",
      image: "https://images.unsplash.com/photo-**********-5c350d0d3c56?w=600&h=400&fit=crop",
      category: "Innovation",
      date: "December 15, 2024",
      author: "Dr. <PERSON>",
      readTime: "5 min read",
      featured: true
    },
    {
      id: 2,
      title: "Digital Radiography: Reducing Radiation Exposure While Enhancing Image Quality",
      excerpt: "Learn about our breakthrough in digital radiography technology that reduces patient radiation exposure by 40% while delivering unprecedented image clarity and diagnostic accuracy.",
      image: "https://images.unsplash.com/photo-1518770660439-4636190af475?w=600&h=400&fit=crop",
      category: "Technology",
      date: "December 10, 2024",
      author: "Dr. Michael Rodriguez",
      readTime: "4 min read",
      featured: false
    },
    {
      id: 3,
      title: "The Future of Patient Monitoring: Smart Sensors and Predictive Analytics",
      excerpt: "Explore how our next-generation patient monitoring systems use IoT sensors and machine learning to predict health complications before they occur, enabling proactive medical interventions.",
      image: "https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=600&h=400&fit=crop",
      category: "Healthcare",
      date: "December 5, 2024",
      author: "Dr. Emily Thompson",
      readTime: "6 min read",
      featured: false
    },
    {
      id: 4,
      title: "Breakthrough in Portable Ultrasound: Point-of-Care Diagnostics Revolution",
      excerpt: "Our new handheld ultrasound device is transforming emergency medicine and rural healthcare by bringing advanced imaging capabilities directly to the patient's bedside.",
      image: "https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?w=600&h=400&fit=crop",
      category: "Innovation",
      date: "November 28, 2024",
      author: "Dr. James Wilson",
      readTime: "7 min read",
      featured: false
    },
    {
      id: 5,
      title: "Cybersecurity in Medical Devices: Protecting Patient Data in the Digital Age",
      excerpt: "Understanding the critical importance of cybersecurity in medical devices and how our comprehensive security protocols ensure patient data protection without compromising functionality.",
      image: "https://images.unsplash.com/photo-**********-824ae1b704d3?w=600&h=400&fit=crop",
      category: "Technology",
      date: "November 20, 2024",
      author: "Dr. Lisa Park",
      readTime: "5 min read",
      featured: false
    },
    {
      id: 6,
      title: "Sustainable Healthcare: Eco-Friendly Medical Equipment Design",
      excerpt: "Discover how we're leading the industry in sustainable medical equipment design, reducing environmental impact while maintaining the highest standards of patient care.",
      image: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=600&h=400&fit=crop",
      category: "Healthcare",
      date: "November 15, 2024",
      author: "Dr. Robert Green",
      readTime: "6 min read",
      featured: false
    }
  ];

  const categories = ['All', 'Innovation', 'Technology', 'Healthcare'];

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Innovation': return 'bg-blue-100 text-blue-800';
      case 'Technology': return 'bg-green-100 text-green-800';
      case 'Healthcare': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredPosts = blogPosts.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.excerpt.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'All' || post.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="max-w-4xl mx-auto">
          {/* Header Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">Medical Technology Blog</h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Stay informed with the latest insights, innovations, and developments in medical technology and healthcare.
            </p>
          </div>

          {/* Search and Filter Section */}
          <div className="flex flex-col md:flex-row gap-4 mb-8">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="Search articles..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div className="flex items-center gap-2">
              <Filter className="h-5 w-5 text-gray-400" />
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
            </div>
          </div>

          {/* Featured Article */}
          {filteredPosts.length > 0 && filteredPosts[0].featured && (
            <div className="mb-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Featured Article</h2>
              <Card className="overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
                  <div className="relative h-64 lg:h-full">
                    <img 
                      src={filteredPosts[0].image} 
                      alt={filteredPosts[0].title}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute top-4 left-4">
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${getCategoryColor(filteredPosts[0].category)}`}>
                        {filteredPosts[0].category}
                      </span>
                    </div>
                  </div>
                  <CardContent className="p-8 flex flex-col justify-center">
                    <div className="flex items-center space-x-4 text-sm text-gray-500 mb-4">
                      <div className="flex items-center space-x-1">
                        <Calendar className="w-4 h-4" />
                        <span>{filteredPosts[0].date}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <User className="w-4 h-4" />
                        <span>{filteredPosts[0].author}</span>
                      </div>
                      <span>{filteredPosts[0].readTime}</span>
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">{filteredPosts[0].title}</h3>
                    <p className="text-gray-600 mb-6 leading-relaxed">{filteredPosts[0].excerpt}</p>
                    <Button className="self-start">
                      Read Full Article
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </CardContent>
                </div>
              </Card>
            </div>
          )}

          {/* Articles Grid */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">All Articles</h2>
            {filteredPosts.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-gray-500 text-lg">No articles found matching your criteria.</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredPosts.slice(filteredPosts[0]?.featured ? 1 : 0).map((post) => (
                  <Card key={post.id} className="overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300">
                    <div className="relative">
                      <img 
                        src={post.image} 
                        alt={post.title}
                        className="w-full h-48 object-cover"
                      />
                      <div className="absolute top-4 left-4">
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${getCategoryColor(post.category)}`}>
                          {post.category}
                        </span>
                      </div>
                    </div>
                    <CardContent className="p-6">
                      <div className="flex items-center space-x-4 text-sm text-gray-500 mb-3">
                        <div className="flex items-center space-x-1">
                          <Calendar className="w-4 h-4" />
                          <span>{post.date}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <User className="w-4 h-4" />
                          <span>{post.author}</span>
                        </div>
                      </div>
                      <h3 className="text-xl font-bold text-gray-900 mb-3">{post.title}</h3>
                      <p className="text-gray-600 mb-4 leading-relaxed">{post.excerpt}</p>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">{post.readTime}</span>
                        <Button variant="outline" size="sm">
                          Read More
                          <ArrowRight className="w-3 h-3 ml-2" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default Blog;
