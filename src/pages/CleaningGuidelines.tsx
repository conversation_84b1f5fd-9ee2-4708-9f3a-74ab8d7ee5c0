
import Header from '@/components/Header';
import Footer from '@/components/Footer';

const CleaningGuidelines = () => {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-gray-900 mb-8 text-center">Cleaning Guidelines</h1>
          
          <div className="space-y-8">
            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Equipment Cleaning & Disinfection</h2>
              <p className="text-gray-600 mb-6">
                Comprehensive guidelines for proper cleaning, disinfection, and maintenance 
                of medical equipment to ensure optimal performance and patient safety.
              </p>
              
              <div className="space-y-6">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-blue-900 mb-3">
                    Daily Cleaning Procedures
                  </h3>
                  <ul className="space-y-2 text-blue-800">
                    <li>• Wipe down all surfaces with approved disinfectant</li>
                    <li>• Clean patient contact areas after each use</li>
                    <li>• Inspect equipment for damage or wear</li>
                    <li>• Document cleaning activities in maintenance log</li>
                  </ul>
                </div>
                
                <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-green-900 mb-3">
                    Weekly Deep Cleaning
                  </h3>
                  <ul className="space-y-2 text-green-800">
                    <li>• Perform thorough cleaning of all components</li>
                    <li>• Check and clean ventilation systems</li>
                    <li>• Calibrate equipment as needed</li>
                    <li>• Update cleaning protocols based on usage</li>
                  </ul>
                </div>
                
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-yellow-900 mb-3">
                    Approved Cleaning Products
                  </h3>
                  <p className="text-yellow-800 mb-3">
                    Only use cleaning products that are compatible with your specific equipment model.
                  </p>
                  <ul className="space-y-2 text-yellow-800">
                    <li>• Isopropyl alcohol (70% concentration)</li>
                    <li>• Hospital-grade disinfectants</li>
                    <li>• Non-abrasive cleaning cloths</li>
                    <li>• Equipment-specific cleaning solutions</li>
                  </ul>
                </div>
              </div>
            </section>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default CleaningGuidelines;
