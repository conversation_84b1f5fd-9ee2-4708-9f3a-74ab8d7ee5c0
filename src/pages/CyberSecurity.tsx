
import Header from '@/components/Header';
import Footer from '@/components/Footer';

const CyberSecurity = () => {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-gray-900 mb-8 text-center">Cyber Security</h1>
          
          <div className="space-y-8">
            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-6 text-center">Medical Device Cyber Security</h2>
              <p className="text-gray-600 mb-6 text-center">
                Comprehensive cybersecurity solutions to protect your medical devices and patient data 
                from evolving digital threats while maintaining operational efficiency.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-red-50 p-6 rounded-lg">
                  <h3 className="text-lg font-medium text-gray-900 mb-3">Threat Assessment</h3>
                  <p className="text-gray-600">
                    Comprehensive evaluation of potential cybersecurity risks and vulnerabilities in your medical equipment network.
                  </p>
                </div>
                
                <div className="bg-blue-50 p-6 rounded-lg">
                  <h3 className="text-lg font-medium text-gray-900 mb-3">Security Updates</h3>
                  <p className="text-gray-600">
                    Regular security patches and firmware updates to protect against the latest cyber threats.
                  </p>
                </div>
                
                <div className="bg-green-50 p-6 rounded-lg">
                  <h3 className="text-lg font-medium text-gray-900 mb-3">Network Security</h3>
                  <p className="text-gray-600">
                    Implementation of secure network protocols and monitoring systems for medical device communications.
                  </p>
                </div>
                
                <div className="bg-purple-50 p-6 rounded-lg">
                  <h3 className="text-lg font-medium text-gray-900 mb-3">Compliance Support</h3>
                  <p className="text-gray-600">
                    Assistance with regulatory compliance requirements for medical device cybersecurity standards.
                  </p>
                </div>
              </div>
            </section>
            
            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4 text-center">Security Best Practices</h2>
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-yellow-900 mb-3">
                  Essential Security Measures
                </h3>
                <ul className="space-y-2 text-yellow-800">
                  <li>• Regular password updates and strong authentication protocols</li>
                  <li>• Network segmentation for medical devices</li>
                  <li>• Real-time monitoring and incident response procedures</li>
                  <li>• Staff training on cybersecurity awareness</li>
                  <li>• Regular security audits and vulnerability assessments</li>
                </ul>
              </div>
            </section>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default CyberSecurity;
