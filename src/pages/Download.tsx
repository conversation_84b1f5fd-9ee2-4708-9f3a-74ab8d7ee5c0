
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Download } from 'lucide-react';

const DownloadPage = () => {
  const handleDownload = (category: string) => {
    // Create a temporary link element to trigger download
    const link = document.createElement('a');
    link.href = '/placeholder.svg'; // Using placeholder as demo file
    link.download = `${category.replace(/\s+/g, '-')}-Resources.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Show success message
    alert(`Downloading ${category} resources...`);
  };

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-center mb-8">
            <div className="mr-4">
              <Download className="h-8 w-8 text-gray-600" />
            </div>
            <h1 className="text-4xl font-bold text-gray-900">Downloads</h1>
          </div>
          
          <div className="space-y-8">
            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Available Downloads</h2>
              <p className="text-gray-600 mb-6">
                Access and download essential resources including software, documentation, 
                and support materials for our medical devices.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-white border rounded-lg p-6 shadow-sm">
                  <div className="flex items-center mb-4">
                    <Download className="h-6 w-6 text-blue-600 mr-3" />
                    <h3 className="text-lg font-semibold text-gray-900">Software Downloads</h3>
                  </div>
                  <p className="text-gray-600 mb-4">
                    Latest software versions, drivers, and system updates for our medical equipment.
                  </p>
                  <button
                    className="text-blue-600 hover:text-blue-800 font-medium"
                    onClick={() => handleDownload('Software Downloads')}
                  >
                    Browse Software →
                  </button>
                </div>
                
                <div className="bg-white border rounded-lg p-6 shadow-sm">
                  <div className="flex items-center mb-4">
                    <Download className="h-6 w-6 text-blue-600 mr-3" />
                    <h3 className="text-lg font-semibold text-gray-900">Product Brochures</h3>
                  </div>
                  <p className="text-gray-600 mb-4">
                    Detailed product specifications, features, and technical documentation.
                  </p>
                  <button
                    className="text-blue-600 hover:text-blue-800 font-medium"
                    onClick={() => handleDownload('Product Brochures')}
                  >
                    Download Brochures →
                  </button>
                </div>
                
                <div className="bg-white border rounded-lg p-6 shadow-sm">
                  <div className="flex items-center mb-4">
                    <Download className="h-6 w-6 text-blue-600 mr-3" />
                    <h3 className="text-lg font-semibold text-gray-900">Training Materials</h3>
                  </div>
                  <p className="text-gray-600 mb-4">
                    Educational resources, training guides, and certification materials.
                  </p>
                  <button
                    className="text-blue-600 hover:text-blue-800 font-medium"
                    onClick={() => handleDownload('Training Materials')}
                  >
                    Access Training →
                  </button>
                </div>
                
                <div className="bg-white border rounded-lg p-6 shadow-sm">
                  <div className="flex items-center mb-4">
                    <Download className="h-6 w-6 text-blue-600 mr-3" />
                    <h3 className="text-lg font-semibold text-gray-900">Compliance Documents</h3>
                  </div>
                  <p className="text-gray-600 mb-4">
                    Regulatory compliance documentation and certification certificates.
                  </p>
                  <button
                    className="text-blue-600 hover:text-blue-800 font-medium"
                    onClick={() => handleDownload('Compliance Documents')}
                  >
                    View Documents →
                  </button>
                </div>
              </div>
            </section>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default DownloadPage;
