
import { useState } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';

const AboutUs = () => {
  const [showPage, setShowPage] = useState(true);

  if (!showPage) {
    return null;
  }

  const scrollToCompanyInfo = () => {
    const element = document.getElementById('company-info');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="flex justify-between items-start mb-8">
          <h1 className="text-3xl font-bold text-gray-900 text-center flex-1">About Us</h1>
          <Button 
            variant="ghost" 
            size="sm"
            onClick={() => setShowPage(false)}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Company Information */}
        <div id="company-info" className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Left side - Company Details */}
          <div className="space-y-6">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">About Medtronix Nepal</h2>
              <div className="space-y-4 text-gray-600 leading-relaxed">
                <p>
                  Medtronix Nepal Pvt.ltd was founded in 2005. It was registered under the Company Registrar's Office Certificate under Company Act 1985. Medtronix was established mainly for the purpose of importing and supplying medical devices to the people of our country Nepal. Medtronix Nepal Pvt.Ltd has become the renowned supplier for medical equipments in Nepal.
                </p>
                <p>
                  The company expanded its operations in other regions of Nepal and has been running the business with great results. At Medtronix we identify ideas that show potential and growth and make them come to life. Unlike a corporate Medtronix, we have the resources to go the distance and nurture ideas from seed to sustainable businesses.
                </p>
                <p>
                  This allows us to offer a more personal approach and guarantees that each and every health organization receives our full attention and support on the way to success.
                </p>
              </div>
            </div>

            <div className="space-y-4">
              <button 
                onClick={scrollToCompanyInfo}
                className="block text-blue-600 hover:text-blue-800 font-medium transition-colors cursor-pointer bg-transparent border-none p-0 text-left"
              >
                Medtronic Healthcare
              </button>
              <a href="#" className="block text-gray-600 hover:text-gray-900 transition-colors">
                News Center
              </a>
              <a href="#" className="block text-gray-600 hover:text-gray-900 transition-colors">
                Events
              </a>
              <a href="#" className="block text-gray-600 hover:text-gray-900 transition-colors">
                Sustainability ↗
              </a>
            </div>
          </div>

          {/* Right side - Image */}
          <div className="w-full">
            <div className="bg-white rounded-lg overflow-hidden shadow-sm">
              <img 
                src="https://images.unsplash.com/photo-1519389950473-47ba0277781c?w=400&h=250&fit=crop" 
                alt="Medical professionals collaborating" 
                className="w-full h-64 object-cover"
              />
              <div className="p-6">
                <p className="text-gray-900 font-medium leading-relaxed">
                  Learn about our company and our latest news in the medical technology industry. We are committed to improving healthcare outcomes across Nepal through innovative medical equipment and dedicated service.
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default AboutUs;
