
import Header from '@/components/Header';
import Footer from '@/components/Footer';

const RMS = () => {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-gray-900 mb-8 text-center">Remote Monitoring Service (RMS)</h1>
          
          <div className="space-y-8">
            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">24/7 Remote Equipment Monitoring</h2>
              <p className="text-gray-600 mb-6">
                Our Remote Monitoring Service provides continuous surveillance of your medical equipment, 
                enabling proactive maintenance and minimizing unexpected downtime.
              </p>
              
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-8 rounded-lg mb-8">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Key Benefits</h3>
                <ul className="space-y-3 text-gray-700">
                  <li className="flex items-start">
                    <span className="text-blue-600 mr-2">•</span>
                    Proactive identification of potential issues before they cause equipment failure
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-600 mr-2">•</span>
                    Reduced unplanned downtime and service calls
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-600 mr-2">•</span>
                    Remote diagnostics and troubleshooting capabilities
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-600 mr-2">•</span>
                    Performance analytics and usage reports
                  </li>
                </ul>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center p-6 border rounded-lg">
                  <h4 className="font-semibold text-gray-900 mb-2">Real-time Monitoring</h4>
                  <p className="text-gray-600 text-sm">
                    Continuous system health monitoring with instant alerts
                  </p>
                </div>
                
                <div className="text-center p-6 border rounded-lg">
                  <h4 className="font-semibold text-gray-900 mb-2">Predictive Analytics</h4>
                  <p className="text-gray-600 text-sm">
                    AI-powered predictions for maintenance needs
                  </p>
                </div>
                
                <div className="text-center p-6 border rounded-lg">
                  <h4 className="font-semibold text-gray-900 mb-2">Expert Support</h4>
                  <p className="text-gray-600 text-sm">
                    Access to certified technicians and engineers
                  </p>
                </div>
              </div>
            </section>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default RMS;
