import { useState } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { X, Phone, Mail, MessageCircle } from 'lucide-react';
import { Link } from 'react-router-dom';

const ComputedTomography = () => {
  const [showPage, setShowPage] = useState(true);

  if (!showPage) {
    return null;
  }

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-gray-800 to-gray-600 text-white py-20">
        <div className="absolute inset-0 opacity-30">
          <img 
            src="https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=1200&h=400&fit=crop" 
            alt="Computed Tomography Equipment" 
            className="w-full h-full object-cover"
          />
        </div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-start">
            <div className="max-w-2xl">
              <h1 className="text-5xl font-bold mb-6">Computed Tomography System</h1>
              <p className="text-xl leading-relaxed">
                Discover advanced CT imaging solutions that deliver precise diagnostics and improved patient outcomes 
                with Samsung Computed Tomography systems.
              </p>
            </div>
            <Button 
              variant="ghost" 
              size="sm"
              className="text-white hover:bg-white/20"
              onClick={() => setShowPage(false)}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </section>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Product Information */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left side - Content */}
          <div className="space-y-8">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">Precision Imaging Technology</h2>
              <p className="text-lg text-gray-600 leading-relaxed mb-6">
                Our Computed Tomography systems provide exceptional cross-sectional imaging with advanced reconstruction 
                algorithms, faster scan times, and comprehensive diagnostic capabilities for complex medical conditions.
              </p>
            </div>

            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <div className="w-2 h-2 bg-green-600 rounded-full mt-3"></div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Multi-Slice Technology</h3>
                  <p className="text-gray-600">Advanced multi-detector arrays for comprehensive imaging</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-4">
                <div className="w-2 h-2 bg-green-600 rounded-full mt-3"></div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Low Dose Protocols</h3>
                  <p className="text-gray-600">Optimized radiation dose management for patient safety</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-4">
                <div className="w-2 h-2 bg-green-600 rounded-full mt-3"></div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">3D Reconstruction</h3>
                  <p className="text-gray-600">Advanced 3D imaging and reconstruction capabilities</p>
                </div>
              </div>
            </div>
          </div>

          {/* Right side - Image */}
          <div className="relative">
            <img 
              src="https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=600&h=400&fit=crop" 
              alt="Computed Tomography System" 
              className="w-full h-96 object-cover rounded-lg shadow-lg"
            />
          </div>
        </div>

        {/* Features Section */}
        <div className="mt-20">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">Advanced Capabilities</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-gray-50 rounded-lg p-6 text-center">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">High-Speed Scanning</h3>
              <p className="text-gray-600">Ultra-fast acquisition times for improved patient comfort</p>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-6 text-center">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">AI-Enhanced Imaging</h3>
              <p className="text-gray-600">Artificial intelligence algorithms for superior image quality</p>
            </div>
            
            <div className="bg-gray-50 rounded-lg p-6 text-center">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Cardiac Imaging</h3>
              <p className="text-gray-600">Specialized protocols for comprehensive cardiac assessment</p>
            </div>
          </div>
        </div>

        {/* Contact Section */}
        <div className="mt-20 bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl p-8 lg:p-12">
          <div className="text-center mb-10">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Ready to Purchase?</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Connect with our sales team to explore our Computed Tomography systems and find the ideal solution for your medical imaging needs.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="text-center p-6 bg-white rounded-lg shadow-sm">
              <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Phone className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Call Sales</h3>
              <p className="text-gray-600 mb-4">Speak directly with our product specialists</p>
              <Button variant="outline" className="border-green-600 text-green-600 hover:bg-green-600 hover:text-white">
                +****************
              </Button>
            </div>

            <div className="text-center p-6 bg-white rounded-lg shadow-sm">
              <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <Mail className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Email Inquiry</h3>
              <p className="text-gray-600 mb-4">Get detailed product information</p>
              <Button variant="outline" className="border-green-600 text-green-600 hover:bg-green-600 hover:text-white">
                <EMAIL>
              </Button>
            </div>

            <div className="text-center p-6 bg-white rounded-lg shadow-sm">
              <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <MessageCircle className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Request Quote</h3>
              <p className="text-gray-600 mb-4">Get a customized quote for your needs</p>
              <Link to="/contact">
                <Button className="bg-green-600 hover:bg-green-700 text-white">
                  Contact Us
                </Button>
              </Link>
            </div>
          </div>

          <div className="text-center">
            <Link to="/contact">
              <Button size="lg" className="bg-green-600 hover:bg-green-700 text-white px-8 py-3">
                Get Started Today
              </Button>
            </Link>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default ComputedTomography;
