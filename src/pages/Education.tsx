
import Header from '@/components/Header';
import Footer from '@/components/Footer';

const Education = () => {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-gray-900 mb-8 text-center">Education</h1>
          
          <div className="space-y-8">
            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">Educational Resources</h2>
              <p className="text-gray-600 mb-6">
                Access comprehensive educational materials, training programs, and learning resources 
                to enhance your knowledge of medical technology and best practices.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-lg font-medium text-gray-900 mb-3">Training Programs</h3>
                  <p className="text-gray-600">
                    Structured learning paths for healthcare professionals to master our medical devices and solutions.
                  </p>
                </div>
                
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-lg font-medium text-gray-900 mb-3">Certification Courses</h3>
                  <p className="text-gray-600">
                    Industry-recognized certification programs to validate your expertise in medical technology.
                  </p>
                </div>
                
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-lg font-medium text-gray-900 mb-3">Webinars & Workshops</h3>
                  <p className="text-gray-600">
                    Live and recorded sessions covering the latest developments in medical imaging and diagnostics.
                  </p>
                </div>
                
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-lg font-medium text-gray-900 mb-3">Clinical Case Studies</h3>
                  <p className="text-gray-600">
                    Real-world examples and case studies to enhance clinical decision-making skills.
                  </p>
                </div>
              </div>
            </section>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default Education;
