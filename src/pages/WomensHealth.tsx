
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';

const WomensHealth = () => {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-pink-50 to-purple-50 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">Women's Health Solutions</h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Comprehensive medical imaging solutions designed specifically for women's healthcare needs, 
              from routine screenings to specialized diagnostics.
            </p>
          </div>
        </div>
      </section>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <img 
                src="https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?w=400&h=250&fit=crop" 
                alt="Ultrasound for Women's Health" 
                className="w-full h-48 object-cover rounded-t-lg"
              />
              <CardTitle>Obstetric Ultrasound</CardTitle>
              <CardDescription>
                Advanced ultrasound systems for prenatal care and monitoring
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Link to="/ultrasound">
                <Button className="w-full">Learn More</Button>
              </Link>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <img 
                src="https://images.unsplash.com/photo-1518770660439-4636190af475?w=400&h=250&fit=crop" 
                alt="Mammography Systems" 
                className="w-full h-48 object-cover rounded-t-lg"
              />
              <CardTitle>Digital Mammography</CardTitle>
              <CardDescription>
                High-resolution digital mammography for breast cancer screening
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Link to="/digital-radiography">
                <Button className="w-full">Learn More</Button>
              </Link>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <img 
                src="https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=400&h=250&fit=crop" 
                alt="CT Imaging" 
                className="w-full h-48 object-cover rounded-t-lg"
              />
              <CardTitle>Gynecological Imaging</CardTitle>
              <CardDescription>
                Specialized CT and MRI solutions for women's health diagnostics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Link to="/computed-tomography">
                <Button className="w-full">Learn More</Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default WomensHealth;
