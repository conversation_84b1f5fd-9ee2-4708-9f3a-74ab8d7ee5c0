
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Calendar } from 'lucide-react';

const Events = () => {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-gray-900 mb-8 text-center">Events</h1>
          
          <div className="space-y-8">
            <section>
              <h2 className="text-2xl font-semibold text-gray-900 mb-6">Upcoming Events</h2>
              
              <div className="space-y-6">
                <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <Calendar className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-medium text-gray-900 mb-2">
                        Medical Technology Conference 2024
                      </h3>
                      <p className="text-gray-600 mb-3">
                        Join us for the annual medical technology conference featuring the latest innovations in healthcare equipment.
                      </p>
                      <div className="text-sm text-gray-500">
                        <p>Date: March 15-17, 2024</p>
                        <p>Location: Kathmandu Convention Center</p>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <Calendar className="h-6 w-6 text-green-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-medium text-gray-900 mb-2">
                        Ultrasound Training Workshop
                      </h3>
                      <p className="text-gray-600 mb-3">
                        Hands-on training workshop for healthcare professionals on advanced ultrasound techniques.
                      </p>
                      <div className="text-sm text-gray-500">
                        <p>Date: February 28, 2024</p>
                        <p>Location: Medtronic Training Center</p>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <Calendar className="h-6 w-6 text-purple-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-medium text-gray-900 mb-2">
                        Product Launch Event
                      </h3>
                      <p className="text-gray-600 mb-3">
                        Exclusive launch event for our newest computed tomography systems.
                      </p>
                      <div className="text-sm text-gray-500">
                        <p>Date: April 10, 2024</p>
                        <p>Location: Hotel Yak & Yeti, Kathmandu</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </section>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default Events;
