
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';

const PointOfCare = () => {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-50 to-cyan-50 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">Point of Care Solutions</h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Portable and efficient medical imaging solutions that bring diagnostic capabilities 
              directly to the patient's bedside for immediate clinical decisions.
            </p>
          </div>
        </div>
      </section>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <img 
                src="https://images.unsplash.com/photo-1531297484001-80022131f5a1?w=400&h=250&fit=crop" 
                alt="Portable Ultrasound" 
                className="w-full h-48 object-cover rounded-t-lg"
              />
              <CardTitle>Portable Ultrasound</CardTitle>
              <CardDescription>
                Compact ultrasound systems for bedside diagnostics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Link to="/ultrasound">
                <Button className="w-full">Learn More</Button>
              </Link>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <img 
                src="https://images.unsplash.com/photo-1518770660439-4636190af475?w=400&h=250&fit=crop" 
                alt="Mobile X-Ray" 
                className="w-full h-48 object-cover rounded-t-lg"
              />
              <CardTitle>Mobile X-Ray</CardTitle>
              <CardDescription>
                Portable digital radiography for critical care environments
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Link to="/digital-radiography">
                <Button className="w-full">Learn More</Button>
              </Link>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <img 
                src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=400&h=250&fit=crop" 
                alt="Emergency Imaging" 
                className="w-full h-48 object-cover rounded-t-lg"
              />
              <CardTitle>Emergency Imaging</CardTitle>
              <CardDescription>
                Rapid diagnostic solutions for emergency departments
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Link to="/computed-tomography">
                <Button className="w-full">Learn More</Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default PointOfCare;
