
import { useState } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ECGHero from '@/components/ecg/ECGHero';
import ECGOverview from '@/components/ecg/ECGOverview';
import ECGProductsSection from '@/components/ecg/ECGProductsSection';
import ECGContact from '@/components/ecg/ECGContact';

const ECG = () => {
  const [showPage, setShowPage] = useState(true);

  if (!showPage) {
    return null;
  }

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <ECGHero onClose={() => setShowPage(false)} />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 space-y-20">
        <ECGOverview />
        <ECGProductsSection />
        <ECGContact />
      </main>

      <Footer />
    </div>
  );
};

export default ECG;
